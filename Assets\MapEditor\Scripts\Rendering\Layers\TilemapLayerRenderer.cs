using System;
using System.Collections.Generic;
using System.IO;
using MapEditor.Core;
using MapEditor.Data.Chunks;
using MapEditor.Rendering.RenderProxy;
using UnityEngine;
using MapEditor.Tools;
using MapEditor.Services;

namespace MapEditor.Rendering.Layers
{
    public class TilemapLayerRenderer : LayerRenderer, ISavable
    {

  
        private bool initialized = false;



        // 存储每个 Chunk 的权重 RenderTexture（0:材质0-3, 1:材质4-7）
        private Dictionary<ChunkCoord, RenderTexture[]> chunkWeightRTs = new();
        // ---> 新增：记录需要持久化保存的脏权重 RT 所在 Chunk
        private readonly HashSet<ChunkCoord> dirtyWeightChunks = new();

        // Shader/Material 缓存
        private static readonly int MaskTexId = Shader.PropertyToID("_MaskTex");
        private Material splatPaintMat;

        private ComputeShader splatCompute;
        private ComputeShader splatBlur;
        private ComputeShader curveStrokeCompute;
        private int csKernel;
        private int csBlurHorizontalKernel;
        private int csBlurVerticalKernel;
        private int csCurveStrokeKernel;

        public override void Initialize(IMapLayer layer)
        {
            base.Initialize(layer);

            // 对 Tilemap 图层进行一次性预构建：数据 Chunk、权重 RT、渲染代理
            if (layer is MapLayer mapLayer)
            {
                int chunkSize = mapLayer.ChunkSize;
                Vector2Int realSize = mapLayer.RealLayerSize;

                int chunkCountX = Mathf.CeilToInt(realSize.x / (float)chunkSize);
                int chunkCountY = Mathf.CeilToInt(realSize.y / (float)chunkSize);

                for (int x = 0; x < chunkCountX; x++)
                {
                    for (int y = 0; y < chunkCountY; y++)
                    {
                        var coord = new ChunkCoord(x, y);

                        // 1) 数据 Chunk
                        TilemapChunk chunk = mapLayer.GetOrCreateChunk<TilemapChunk>(coord);

                        // 2) 权重 RT（两张）
                        var rts = GetOrCreateChunkRTs(coord);

                        // 3) 渲染代理
                        if (!proxies.TryGetValue(coord, out var proxy))
                        {
                            proxy = CreateProxy(coord, chunk);
                            if (proxy != null)
                            {
                                proxies[coord] = proxy;
                            }
                        }

                        // 4) 将权重 RT 绑定到代理 & 刷新材质贴图映射
                        if (proxy is TilemapChunkRenderProxy tp)
                        {
                            tp.UpdateWeightTextures(rts);
                            tp.ApplySurfaceTextureMapping();
                        }
                    }
                }
            }

            initialized = true;

            // 注册到 SaveService 以统一保存
            if (MapEditorCore.Instance.GetService<SaveService>() != null)
            {
                MapEditorCore.Instance.GetService<SaveService>().RegisterSavable(this);
            }
        }


        private void EnsureMaterials()
        {
            if (splatPaintMat == null)
            {
                var shader = Shader.Find("MapEditor/SplatPaint");
                if (shader == null)
                {
                    Debug.LogError("SplatPaint shader not found");
                    return;
                }
                splatPaintMat = new Material(shader);
            }
        }

        private void EnsureCompute()
        {
            if (splatCompute == null)
            {
                splatCompute = Resources.Load<ComputeShader>("Shaders/SplatBrush");
                if (splatCompute != null)
                    csKernel = splatCompute.FindKernel("CSMain");
                else Debug.LogError("SplatBrush.compute not found in Resources/Shaders");
            }
            
            if (splatBlur == null)
            {
                splatBlur = Resources.Load<ComputeShader>("Shaders/SplatBlur");
                if (splatBlur != null)
                {
                    csBlurHorizontalKernel = splatBlur.FindKernel("CSBlurHorizontal");
                    csBlurVerticalKernel = splatBlur.FindKernel("CSBlurVertical");
                }
                else Debug.LogError("SplatBlur.compute not found in Resources/Shaders");
            }
            
            if (curveStrokeCompute == null)
            {
                curveStrokeCompute = Resources.Load<ComputeShader>("Shaders/CurveStroke");
                if (curveStrokeCompute != null)
                    csCurveStrokeKernel = curveStrokeCompute.FindKernel("CSCurveStroke");
                else Debug.LogError("CurveStroke.compute not found in Resources/Shaders");
            }
        }

        /// <summary>
        /// GPU 路径：根据 BrushSettings 在指定世界坐标绘制权重。
        /// </summary>
        public void DrawBrushGPU(Vector2 worldPosition, BrushSettings settings)
        {
            EnsureMaterials(); // 确保材质先创建
            EnsureCompute();
            if (splatPaintMat == null)
            {
                Debug.LogError("[DrawBrushGPU] splatPaintMat is null, abort drawing");
                return;
            }
            if (!initialized) return;

            // 若世界中心点不在有效图层范围内则直接返回
            if (!IsWorldPositionInsideLayer(worldPosition)) return;

            // 找到像素中心和 chunk
            Vector2Int pixelCenter = WorldToPixel(worldPosition);
            int chunkX = Mathf.FloorToInt((float)pixelCenter.x / LinkedLayer.ChunkSize);
            int chunkY = Mathf.FloorToInt((float)pixelCenter.y / LinkedLayer.ChunkSize);
            var coord = new ChunkCoord(chunkX, chunkY);

            // 获取或创建权重 RT
            var rts = GetOrCreateChunkRTs(coord);

            // 生成 MaskRT
            int maskPixelSize = Mathf.CeilToInt(settings.size * MapEditor.Core.MapEditorConfig.PixelsPerUnit);
            if (maskPixelSize <= 0) {
                Debug.LogWarning("[DrawBrushGPU] maskPixelSize<=0");
                return; }
            RenderTexture maskRT = RenderTexture.GetTemporary(maskPixelSize, maskPixelSize, 0, RenderTextureFormat.R8);
            maskRT.enableRandomWrite = false;
            maskRT.Create();

            // 生成 MaskRT 内容: alphaMask * strength
            var alphaMaskTex = settings.shape != null && settings.shape.alphaMask != null ? settings.shape.alphaMask : Texture2D.whiteTexture;
            splatPaintMat.SetFloat("_Strength", settings.strength);
            Graphics.Blit(alphaMaskTex, maskRT, splatPaintMat, 0);

            // 计算画笔半径像素
            int radiusPx = maskPixelSize / 2;
            int pixelMinX = pixelCenter.x - radiusPx;
            int pixelMinY = pixelCenter.y - radiusPx;
            int pixelMaxX = pixelCenter.x + radiusPx;
            int pixelMaxY = pixelCenter.y + radiusPx;

            int chunkMinX = Mathf.FloorToInt((float)pixelMinX / LinkedLayer.ChunkSize);
            int chunkMaxX = Mathf.FloorToInt((float)pixelMaxX / LinkedLayer.ChunkSize);
            int chunkMinY = Mathf.FloorToInt((float)pixelMinY / LinkedLayer.ChunkSize);
            int chunkMaxY = Mathf.FloorToInt((float)pixelMaxY / LinkedLayer.ChunkSize);

            // 设置 maskTex once
            splatCompute.SetTexture(csKernel, "_MaskTex", maskRT);
            splatCompute.SetInt("_MaskSize", maskPixelSize);
            int groups = Mathf.CeilToInt(maskPixelSize / 8f);
            
            // Pass 1: 绘制权重，并收集受影响的区块
            var splattedChunks = new HashSet<ChunkCoord>();
            for(int cx = chunkMinX; cx<=chunkMaxX; cx++)
            {
                for(int cy = chunkMinY; cy<=chunkMaxY; cy++)
                {
                    var chunkCoord = new ChunkCoord(cx, cy);

                    // 边界检查：确保 chunkCoord 在图层定义的范围内
                    if (LinkedLayer is MapLayer mapLayer)
                    {
                        int chunkCountX = Mathf.CeilToInt(mapLayer.LayerSize.x / (float)mapLayer.ChunkSize);
                        int chunkCountY = Mathf.CeilToInt(mapLayer.LayerSize.y / (float)mapLayer.ChunkSize);
                        if (cx < 0 || cx >= chunkCountX || cy < 0 || cy >= chunkCountY)
                        {
                            continue; // 跳过界外 Chunk
                        }
                    }
 
                     // ========= 1) 获取 / 创建 数据 Chunk =========
                    TilemapChunk tilemapChunk = (LinkedLayer as MapLayer)?.GetOrCreateChunk<TilemapChunk>(chunkCoord);
                    if (tilemapChunk == null) continue;
 
                     // ========= 2) 计算 Chunk 内通道 =========
                    int channel = tilemapChunk.GetOrAssignChannel(settings.materialIndex);
                    if (channel < 0)
                    {
                        // 通道已满，警告已由内部抛出
                        continue;
                    }
 
                    // ========= 3) 准备权重 RT =========
                    var rtsLocal = GetOrCreateChunkRTs(chunkCoord);
 
                     // ========= 4) Bind Compute 参数 =========
                    splatCompute.SetTexture(csKernel, "_Splat0", rtsLocal[0]);
                    splatCompute.SetTexture(csKernel, "_Splat1", rtsLocal[1]);
 
                    int chunkOriginPxX = cx * LinkedLayer.ChunkSize;
                    int chunkOriginPxY = cy * LinkedLayer.ChunkSize;
                    int offsetPxX = pixelMinX - chunkOriginPxX;
                    int offsetPxY = pixelMinY - chunkOriginPxY;
                    splatCompute.SetInts("_OffsetPx", offsetPxX, offsetPxY);
                    splatCompute.SetInt("_Channel", channel);
                    // 将 UI 的 0(soft)-1(hard) 硬度值映射到 4.0-0.25 的指数范围
                    float mappedHardness = Mathf.Lerp(4.0f, 0.25f, settings.hardness);
                    splatCompute.SetFloat("_Hardness", mappedHardness);
 
                    splatCompute.Dispatch(csKernel, groups, groups, 1);
 
                    // ========= 5) 确保/更新渲染代理 =========
                    if (!proxies.TryGetValue(chunkCoord, out var p))
                    {
                        var newProxy = CreateProxy(chunkCoord, tilemapChunk) as TilemapChunkRenderProxy;
                        if (newProxy != null)
                        {
                            proxies[chunkCoord] = newProxy;
                            p = newProxy;
                        }
                    }
                    if (p is TilemapChunkRenderProxy tp)
                    {
                        tp.UpdateWeightTextures(rtsLocal);
                        tp.ApplySurfaceTextureMapping();
                    }
 
                    // ========= 6) 标记需保存 & 收集已绘制区块 =========
                    dirtyWeightChunks.Add(chunkCoord);
                    splattedChunks.Add(chunkCoord);
                }
            }
            
            RenderTexture.ReleaseTemporary(maskRT);

            // Pass 2: 对受影响的区块及其邻居执行模糊处理
            if (settings.blurRadius > 0 && splatBlur != null)
            {
                var chunksToBlur = new HashSet<ChunkCoord>();
                foreach (var splattedCoord in splattedChunks)
                {
                    // 添加自身及其8个邻居
                    for (int dx = -1; dx <= 1; dx++)
                    {
                        for (int dy = -1; dy <= 1; dy++)
                        {
                            chunksToBlur.Add(new ChunkCoord(splattedCoord.X + dx, splattedCoord.Y + dy));
                        }
                    }
                }

                int blurGroups = Mathf.CeilToInt(LinkedLayer.ChunkSize / 8f);
                splatBlur.SetInt("_BlurRadius", settings.blurRadius);
                splatBlur.SetFloat("_BilateralFactor", settings.bilateralFactor);

                foreach (var blurCoord in chunksToBlur)
                {
                    // 检查区块是否存在，避免处理地图外的区域
                    if (!chunkWeightRTs.ContainsKey(blurCoord)) continue;
                    
                    BlurChunkWithPadding(blurCoord, settings);
                }
            }

           // Debug.Log($"[DrawBrushGPU] Painted brush, sizePx {maskPixelSize}, affected chunks {chunkMinX},{chunkMinY} to {chunkMaxX},{chunkMaxY}");
        }

        /// <summary>
        /// GPU路径：根据曲线段绘制描边
        /// </summary>
        public void DrawCurveStrokeGPU(System.Collections.Generic.IReadOnlyList<MapEditor.Tools.CurveSegment> curveSegments, 
            MapEditor.Tools.CurveStrokeSettings settings)
        {
            EnsureCompute();
            if (curveStrokeCompute == null)
            {
                Debug.LogError("[DrawCurveStrokeGPU] curveStrokeCompute is null, abort drawing");
                return;
            }
            if (!initialized || curveSegments.Count < 2) return;

            // 计算曲线包围盒
            Bounds curveBounds = CalculateCurveBounds(curveSegments, settings.width);
            
            // 转换为像素坐标
            Vector2Int minPixel = WorldToPixel(curveBounds.min);
            Vector2Int maxPixel = WorldToPixel(curveBounds.max);
            
            // 计算影响的Chunk范围
            int chunkMinX = Mathf.FloorToInt((float)minPixel.x / LinkedLayer.ChunkSize);
            int chunkMaxX = Mathf.FloorToInt((float)maxPixel.x / LinkedLayer.ChunkSize);
            int chunkMinY = Mathf.FloorToInt((float)minPixel.y / LinkedLayer.ChunkSize);
            int chunkMaxY = Mathf.FloorToInt((float)maxPixel.y / LinkedLayer.ChunkSize);

            // 创建GPU缓冲区
            using var segmentBuffer = CreateCurveSegmentBuffer(curveSegments);
            
            // 设置公共参数
            curveStrokeCompute.SetBuffer(csCurveStrokeKernel, "_CurveSegments", segmentBuffer);
            curveStrokeCompute.SetFloat("_StrokeWidth", settings.width);
            curveStrokeCompute.SetFloat("_EdgeSoftness", settings.edgeSoftness);
            curveStrokeCompute.SetFloat("_Strength", settings.strength);
            curveStrokeCompute.SetInt("_SegmentCount", curveSegments.Count);
            curveStrokeCompute.SetFloat("_PixelsPerUnit", MapEditor.Core.MapEditorConfig.PixelsPerUnit);
            
            // 新增：传递硬度参数
            // 将 [0, 1] 的硬度值映射到 [0.25, 4.0] 的指数范围
            // hardness = 0 (soft) -> exponent = 0.25
            // hardness = 1 (hard) -> exponent = 4.0
            float mappedHardness = Mathf.Lerp(0.25f, 4.0f, settings.hardness);
            curveStrokeCompute.SetFloat("_Hardness", mappedHardness);

            var affectedChunks = new System.Collections.Generic.HashSet<ChunkCoord>();

            // 对每个影响的Chunk进行绘制
            for (int cx = chunkMinX; cx <= chunkMaxX; cx++)
            {
                for (int cy = chunkMinY; cy <= chunkMaxY; cy++)
                {
                    var chunkCoord = new ChunkCoord(cx, cy);

                    // 边界检查
                    if (LinkedLayer is MapLayer mapLayer)
                    {
                        int chunkCountX = Mathf.CeilToInt(mapLayer.LayerSize.x / (float)mapLayer.ChunkSize);
                        int chunkCountY = Mathf.CeilToInt(mapLayer.LayerSize.y / (float)mapLayer.ChunkSize);
                        if (cx < 0 || cx >= chunkCountX || cy < 0 || cy >= chunkCountY)
                        {
                            continue;
                        }
                    }

                    // 获取或创建数据Chunk
                    TilemapChunk tilemapChunk = (LinkedLayer as MapLayer)?.GetOrCreateChunk<TilemapChunk>(chunkCoord);
                    if (tilemapChunk == null) continue;

                    // 计算通道
                    int channel = tilemapChunk.GetOrAssignChannel(settings.materialIndex);
                    if (channel < 0) continue;

                    // 获取权重RT
                    var rts = GetOrCreateChunkRTs(chunkCoord);

                    // 设置Chunk特定参数
                    Vector2 chunkWorldOffset = new Vector2(cx * LinkedLayer.ChunkSize, cy * LinkedLayer.ChunkSize) / MapEditor.Core.MapEditorConfig.PixelsPerUnit;
                    curveStrokeCompute.SetVector("_ChunkOffset", chunkWorldOffset);
                    curveStrokeCompute.SetInt("_Channel", channel);

                    // 直接绑定Splat贴图进行读写
                    curveStrokeCompute.SetTexture(csCurveStrokeKernel, "_Splat0", rts[0]);
                    curveStrokeCompute.SetTexture(csCurveStrokeKernel, "_Splat1", rts[1]);

                    // 执行计算
                    int groups = Mathf.CeilToInt(LinkedLayer.ChunkSize / 8f);
                    curveStrokeCompute.Dispatch(csCurveStrokeKernel, groups, groups, 1);

                    // 更新渲染代理
                    UpdateChunkProxy(chunkCoord, tilemapChunk, rts);

                    // 标记为脏数据
                    dirtyWeightChunks.Add(chunkCoord);
                    affectedChunks.Add(chunkCoord);
                }
            }

            Debug.Log($"[DrawCurveStrokeGPU] Drew curve stroke, affected chunks: {affectedChunks.Count}");
        }

        /// <summary>
        /// 计算曲线包围盒
        /// </summary>
        private Bounds CalculateCurveBounds(System.Collections.Generic.IReadOnlyList<MapEditor.Tools.CurveSegment> curveSegments, float strokeWidth)
        {
            if (curveSegments.Count == 0) return new Bounds();

            Vector2 min = curveSegments[0].position;
            Vector2 max = curveSegments[0].position;

            foreach (var segment in curveSegments)
            {
                min = Vector2.Min(min, segment.position);
                max = Vector2.Max(max, segment.position);
            }

            // 扩展包围盒以包含描边宽度
            float halfWidth = strokeWidth * 0.5f;
            min -= Vector2.one * halfWidth;
            max += Vector2.one * halfWidth;

            Vector2 center = (min + max) * 0.5f;
            Vector2 size = max - min;
            return new Bounds(center, size);
        }

        /// <summary>
        /// 创建曲线段GPU缓冲区
        /// </summary>
        private ComputeBuffer CreateCurveSegmentBuffer(System.Collections.Generic.IReadOnlyList<MapEditor.Tools.CurveSegment> curveSegments)
        {
            // GPU结构体大小：float2 position + float2 tangent + float2 normal + float distance + float width = 8 * 4 = 32 bytes
            var buffer = new ComputeBuffer(curveSegments.Count, 32);
            
            var data = new float[curveSegments.Count * 8];
            for (int i = 0; i < curveSegments.Count; i++)
            {
                var segment = curveSegments[i];
                int offset = i * 8;
                data[offset + 0] = segment.position.x;
                data[offset + 1] = segment.position.y;
                data[offset + 2] = segment.tangent.x;
                data[offset + 3] = segment.tangent.y;
                data[offset + 4] = segment.normal.x;
                data[offset + 5] = segment.normal.y;
                data[offset + 6] = segment.distance;
                data[offset + 7] = 1.0f; // width字段，暂时设为1.0
            }
            
            buffer.SetData(data);
            return buffer;
        }

        /// <summary>
        /// 将遮罩应用到权重纹理
        /// </summary>
        private void ApplyMaskToWeightTexture(RenderTexture mask, RenderTexture[] weightTextures, int channel, float strength)
        {
            // 使用现有的SplatBrush计算着色器来应用遮罩
            if (splatCompute == null) return;

            splatCompute.SetTexture(csKernel, "_MaskTex", mask);
            splatCompute.SetTexture(csKernel, "_Splat0", weightTextures[0]);
            splatCompute.SetTexture(csKernel, "_Splat1", weightTextures[1]);
            splatCompute.SetInt("_MaskSize", mask.width);
            splatCompute.SetInts("_OffsetPx", 0, 0); // 无偏移，直接覆盖
            splatCompute.SetInt("_Channel", channel);
            splatCompute.SetFloat("_Hardness", 1.0f); // 使用硬边缘，因为柔和度已经在距离场中处理

            int groups = Mathf.CeilToInt(mask.width / 8f);
            splatCompute.Dispatch(csKernel, groups, groups, 1);
        }

        /// <summary>
        /// 更新Chunk渲染代理
        /// </summary>
        private void UpdateChunkProxy(ChunkCoord coord, TilemapChunk chunk, RenderTexture[] rts)
        {
            if (!proxies.TryGetValue(coord, out var proxy))
            {
                var newProxy = CreateProxy(coord, chunk) as TilemapChunkRenderProxy;
                if (newProxy != null)
                {
                    proxies[coord] = newProxy;
                    proxy = newProxy;
                }
            }

            if (proxy is TilemapChunkRenderProxy tp)
            {
                tp.UpdateWeightTextures(rts);
                tp.ApplySurfaceTextureMapping();
            }
        }

        /// <summary>
        /// 对单个 Chunk 执行带边缘填充的无缝模糊处理。
        /// </summary>
        private void BlurChunkWithPadding(ChunkCoord coord, BrushSettings settings)
        {
            int radius = settings.blurRadius;
            if (radius <= 0) return;

            int chunkSize = LinkedLayer.ChunkSize;
            int paddedSize = chunkSize + radius * 2;

            // 1. 获取或创建临时的大纹理
            RenderTexture tempSplat0 = RenderTexture.GetTemporary(paddedSize, paddedSize, 0, RenderTextureFormat.ARGB32);
            RenderTexture tempSplat1 = RenderTexture.GetTemporary(paddedSize, paddedSize, 0, RenderTextureFormat.ARGB32);
            tempSplat0.enableRandomWrite = true;
            tempSplat1.enableRandomWrite = true;

            // 2. "缝合"纹理：将中心和8个邻居的像素复制到临时纹理
            for (int dx = -1; dx <= 1; dx++)
            {
                for (int dy = -1; dy <= 1; dy++)
                {
                    var neighborCoord = new ChunkCoord(coord.X + dx, coord.Y + dy);
                    if (!chunkWeightRTs.TryGetValue(neighborCoord, out var neighborRTs))
                    {
                        // 如果邻居不存在（例如在地图边缘），可以跳过或用黑色填充
                        continue;
                    }

                    // 最终修正：分别处理9个区域，确保坐标和尺寸正确无误
                    int srcX, srcY, dstX, dstY, copyWidth, copyHeight;

                    if (dx == 0 && dy == 0) // 中心
                    {
                        srcX = 0; srcY = 0;
                        dstX = radius; dstY = radius;
                        copyWidth = chunkSize; copyHeight = chunkSize;
                    }
                    else if (dx == 0) // 上/下
                    {
                        srcX = 0; srcY = (dy > 0) ? 0 : chunkSize - radius;
                        dstX = radius; dstY = (dy > 0) ? chunkSize + radius : 0;
                        copyWidth = chunkSize; copyHeight = radius;
                    }
                    else if (dy == 0) // 左/右
                    {
                        srcX = (dx > 0) ? 0 : chunkSize - radius; srcY = 0;
                        dstX = (dx > 0) ? chunkSize + radius : 0; dstY = radius;
                        copyWidth = radius; copyHeight = chunkSize;
                    }
                    else // 四个角
                    {
                        srcX = (dx > 0) ? 0 : chunkSize - radius;
                        srcY = (dy > 0) ? 0 : chunkSize - radius;
                        dstX = (dx > 0) ? chunkSize + radius : 0;
                        dstY = (dy > 0) ? chunkSize + radius : 0;
                        copyWidth = radius; copyHeight = radius;
                    }

                    Graphics.CopyTexture(neighborRTs[0], 0, 0, srcX, srcY, copyWidth, copyHeight, tempSplat0, 0, 0, dstX, dstY);
                    Graphics.CopyTexture(neighborRTs[1], 0, 0, srcX, srcY, copyWidth, copyHeight, tempSplat1, 0, 0, dstX, dstY);
                }
            }

            // 3. 在临时纹理上执行模糊
            int blurGroups = Mathf.CeilToInt(paddedSize / 8f);
            splatBlur.SetInt("_BlurRadius", radius);
            splatBlur.SetFloat("_BilateralFactor", settings.bilateralFactor);

            // 水平模糊
            splatBlur.SetTexture(csBlurHorizontalKernel, "_Splat0", tempSplat0);
            splatBlur.SetTexture(csBlurHorizontalKernel, "_Splat1", tempSplat1);
            splatBlur.Dispatch(csBlurHorizontalKernel, blurGroups, blurGroups, 1);

            // 垂直模糊
            splatBlur.SetTexture(csBlurVerticalKernel, "_Splat0", tempSplat0);
            splatBlur.SetTexture(csBlurVerticalKernel, "_Splat1", tempSplat1);
            splatBlur.Dispatch(csBlurVerticalKernel, blurGroups, blurGroups, 1);

            // 4. 将结果写回原始纹理
            var originalRTs = GetOrCreateChunkRTs(coord);
            Graphics.CopyTexture(tempSplat0, 0, 0, radius, radius, chunkSize, chunkSize, originalRTs[0], 0, 0, 0, 0);
            Graphics.CopyTexture(tempSplat1, 0, 0, radius, radius, chunkSize, chunkSize, originalRTs[1], 0, 0, 0, 0);

            // 5. 释放临时纹理
            RenderTexture.ReleaseTemporary(tempSplat0);
            RenderTexture.ReleaseTemporary(tempSplat1);
        }

        private RenderTexture[] GetOrCreateChunkRTs(ChunkCoord coord)
        {
            if (chunkWeightRTs.TryGetValue(coord, out var arr)) return arr;

            int texSize = LinkedLayer.ChunkSize;
            RenderTexture CreateRT()
            {
                RenderTexture rt = new(texSize, texSize, 0, RenderTextureFormat.ARGB32)
                {
                    filterMode = FilterMode.Bilinear,
                    enableRandomWrite = true
                };
                rt.Create();
                return rt;
            }

            var rtArray = new[] { CreateRT(), CreateRT() };
            // ---> 尝试从磁盘加载已保存的权重贴图到新创建的 RT 中
            LoadWeightTextures(coord, rtArray);

            chunkWeightRTs[coord] = rtArray;

            if (proxies.TryGetValue(coord, out var p) && p is TilemapChunkRenderProxy tp)
            {
                tp.UpdateWeightTextures(rtArray);
            }
            return rtArray;
        }

        /// <summary>
        /// 公共方法：获取或创建指定坐标的权重RenderTexture数组
        /// </summary>
        /// <param name="coord">Chunk坐标</param>
        /// <returns>权重RT数组</returns>
        public RenderTexture[] GetOrCreateChunkRTsPublic(ChunkCoord coord)
        {
            return GetOrCreateChunkRTs(coord);
        }

        /// <summary>
        /// 标记指定chunk的权重RT为脏，确保会被自动保存
        /// </summary>
        /// <param name="coord">Chunk坐标</param>
        public void MarkChunkWeightsDirty(ChunkCoord coord)
        {
            dirtyWeightChunks.Add(coord);
        }

        /// <summary>
        /// 刷新所有渲染代理的材质映射，通常在chunk的材质配置变更后调用
        /// </summary>
        public void RefreshAllProxyMaterialMappings()
        {
            Debug.Log($"正在刷新 {proxies.Count} 个渲染代理的材质映射");
            foreach (var kvp in proxies)
            {
                if (kvp.Value is TilemapChunkRenderProxy proxy)
                {
                    // 强制刷新材质映射
                    proxy.ApplySurfaceTextureMapping();
                    
                    // 也确保权重RT绑定正确
                    var coord = kvp.Key;
                    var rts = GetOrCreateChunkRTsPublic(coord);
                    proxy.UpdateWeightTextures(rts);
                }
            }
        }

        public override void RefreshChunks(Bounds viewBounds)
        {
            base.RefreshChunks(viewBounds);
        }



        protected override ChunkRenderProxy CreateProxy(ChunkCoord coord, ChunkBase chunk)
        {
            // 若传入的 chunk 为空, 再次确保从数据层获取并写入
            TilemapChunk tilemapChunk = chunk as TilemapChunk;
            if (tilemapChunk == null)
            {
                tilemapChunk = (LinkedLayer as MapLayer)?.GetOrCreateChunk<TilemapChunk>(coord);
            }

            return new TilemapChunkRenderProxy(coord, tilemapChunk, this);
        }

        /// <summary>
        /// 图层移除时的资源清理。遍历所有 Chunk 渲染代理并删除对应磁盘上的贴图资源文件。
        /// </summary>
        public override void OnLayerRemoved()
        {
            var mapDataStore = MapEditorCore.Instance.MapDataStore;
            var mapDir = mapDataStore?.CurrentMapDirectory;
            if (string.IsNullOrEmpty(mapDir)) return;

            string texturesDir = Path.Combine(mapDir, "TilemapTextures");

            foreach (var kv in proxies)
            {
                if (kv.Value is TilemapChunkRenderProxy proxy && proxy.TilemapChunk != null)
                {
                    string fileName = proxy.TilemapChunk.TilemapId + ".raw";
                    string filePath = Path.Combine(texturesDir, fileName);

                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                }
            }
        }

        /// <summary>
        /// 根据已反序列化的 Chunk 数据重新创建渲染代理(用于加载地图)。
        /// </summary>
        public void BuildProxiesFromSavedChunks(MapLayer mapLayer)
        {
            if (mapLayer == null) return;

            foreach (var chunk in mapLayer.GetAllChunks())
            {
                var coord = chunk.Coord;
                if (!proxies.TryGetValue(coord, out var proxy))
                {
                    proxy = CreateProxy(coord, chunk);
                    if (proxy != null)
                    {
                        proxies[coord] = proxy;
                    }
                }

                // ---> 确保权重贴图加载并与代理绑定
                var rts = GetOrCreateChunkRTs(coord);
                if (proxy is TilemapChunkRenderProxy tp)
                {
                    tp.UpdateWeightTextures(rts);
                    tp.ApplySurfaceTextureMapping();
                }
            }
        }


        #region ISavable 实现

        public void Save(SaveContext context)
        {
            AutoSaveDirtyChunks();
            AutoSaveDirtyWeightRTs();
            CleanOrphans(context.MapDirectory);
        }

        /// <summary>
        /// 遍历所有 TilemapChunkRenderProxy, 将脏数据保存到磁盘。
        /// </summary>
        private void AutoSaveDirtyChunks()
        {
            int savedCount = 0;
            foreach (var kv in proxies)
            {
                if (kv.Value is TilemapChunkRenderProxy proxy && proxy.IsDirty)
                {
                    proxy.SaveTexture();
                    proxy.MarkClean();
                    savedCount++;
                }
            }

            if (savedCount > 0)
            {
                Debug.Log($"[TilemapLayerRenderer] Auto-saved {savedCount} dirty chunk textures.");
            }
        }

        /// <summary>
        /// 保存所有脏权重RT到磁盘
        /// </summary>
        private void AutoSaveDirtyWeightRTs()
        {
            if (dirtyWeightChunks.Count == 0) return;
            var mapDataStore = MapEditorCore.Instance.MapDataStore;
            var mapDir = mapDataStore?.CurrentMapDirectory;
            if (string.IsNullOrEmpty(mapDir)) return;

            string weightsDir = System.IO.Path.Combine(mapDir, "TilemapWeights");
            if (!System.IO.Directory.Exists(weightsDir)) System.IO.Directory.CreateDirectory(weightsDir);

            int saved = 0;
            var toSave = new List<ChunkCoord>(dirtyWeightChunks);
            foreach (var coord in toSave)
            {
                if (!chunkWeightRTs.TryGetValue(coord, out var rts)) continue;
                TilemapChunk chunk = (LinkedLayer as MapLayer)?.GetOrCreateChunk<TilemapChunk>(coord);
                if (chunk == null) continue;

                string baseName = chunk.TilemapId;
                string file0 = System.IO.Path.Combine(weightsDir, baseName + "_splat0.raw");
                string file1 = System.IO.Path.Combine(weightsDir, baseName + "_splat1.raw");

                SaveRenderTextureToRaw(rts[0], file0);
                SaveRenderTextureToRaw(rts[1], file1);
                saved++;
                dirtyWeightChunks.Remove(coord);
            }

            if (saved > 0)
            {
                Debug.Log($"[TilemapLayerRenderer] Auto-saved {saved} dirty weight RT pairs.");
            }
        }

        /// <summary>
        /// 将 RenderTexture 内容以 RAW-RGBA32 保存。
        /// </summary>
        private void SaveRenderTextureToRaw(RenderTexture rt, string filePath)
        {
            if (rt == null) return;
            var prev = RenderTexture.active;
            RenderTexture.active = rt;
            Texture2D tex = new Texture2D(rt.width, rt.height, TextureFormat.RGBA32, false);
            tex.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
            tex.Apply();
            RenderTexture.active = prev;

            MapEditor.Utility.RawTextureUtility.SaveTexture(tex, filePath);
            UnityEngine.Object.Destroy(tex);
        }

        private void CleanOrphans(string mapDir)
        {
            if (string.IsNullOrEmpty(mapDir)) return;
            // 收集当前渲染器管理的有效 TilemapId
            var validIds = new HashSet<string>();
            foreach (var kv in proxies)
            {
                if (kv.Value is TilemapChunkRenderProxy tp && tp.TilemapChunk != null)
                {
                    validIds.Add(tp.TilemapChunk.TilemapId);
                }
            }

            void ProcessDir(string dir, Func<string, string> extractId)
            {
                if (!System.IO.Directory.Exists(dir)) return;
                foreach (var file in System.IO.Directory.GetFiles(dir, "*.raw"))
                {
                    var name = System.IO.Path.GetFileNameWithoutExtension(file);
                    var id = extractId(name);
                    if (!validIds.Contains(id))
                    {
                        try { System.IO.File.Delete(file); }
                        catch (Exception ex) { Debug.LogError($"[TilemapLayerRenderer] 删除孤立贴图失败: {file} -> {ex.Message}"); }
                    }
                }
            }

            string texturesDir = System.IO.Path.Combine(mapDir, "TilemapTextures");
            string weightsDir = System.IO.Path.Combine(mapDir, "TilemapWeights");

            ProcessDir(texturesDir, n => n);
            ProcessDir(weightsDir, n => n.Split('_')[0]);
        }

        #endregion

        // ======= 权重贴图加载/保存辅助 (Editor & Player) =======
        private void LoadWeightTextures(ChunkCoord coord, RenderTexture[] rts)
        {
            var mapDataStore = MapEditorCore.Instance.MapDataStore;
            var mapDir = mapDataStore?.CurrentMapDirectory;
            if (string.IsNullOrEmpty(mapDir)) return;
            string weightsDir = Path.Combine(mapDir, "TilemapWeights");

            TilemapChunk chunk = (LinkedLayer as MapLayer)?.GetOrCreateChunk<TilemapChunk>(coord);
            if (chunk == null) return;
            string baseName = chunk.TilemapId;
            string file0 = Path.Combine(weightsDir, baseName + "_splat0.raw");
            string file1 = Path.Combine(weightsDir, baseName + "_splat1.raw");

            CopyRawToRenderTexture(file0, rts[0]);
            CopyRawToRenderTexture(file1, rts[1]);
        }

        private void CopyRawToRenderTexture(string filePath, RenderTexture dest)
        {
            var tex = MapEditor.Utility.RawTextureUtility.LoadTexture(filePath);
            if (tex == null) return;
            Graphics.Blit(tex, dest);
            UnityEngine.Object.Destroy(tex);
        }



    }
}