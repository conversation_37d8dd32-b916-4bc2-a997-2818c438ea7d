# CurveTool 融合效果优化文档

- **版本**: 1.0
- **日期**: 2025-07-04
- **目标**: 提升曲线工具的绘制效果，使其能够与现有的Tilemap地貌进行更自然、更无缝的视觉融合，打破人工绘制的边界感，增强环境的真实性和沉浸感。

---

## 一、 高优先级待办事项 (High Priority Tasks)

### 任务 1: 实现“边缘渗透”与“程序化噪声”

**目标**: 消除当前曲线边缘过于平滑、均匀的渐变，模拟自然风化或生长的随机边界，使其看起来像是地貌的一部分，而非“贴图”。

**具体实施项**:

1.  **修改 `CurveStroke.compute` 着色器**:
    *   在着色器内部引入一个高效的程序化噪声函数（例如 Perlin 或 Simplex 噪声）。
    *   在计算像素到曲线的距离后，使用噪声函数对该距离进行小幅度的、可控的扰动。

2.  **新增工具参数 (`CurveStrokeSettings`)**:
    *   `edgeNoiseStrength` (float): 控制噪声扰动的强度，即边缘不规则的程度。
    *   `edgeNoiseScale` (float): 控制噪声的频率，决定了边缘细节的粗糙或细腻程度。

3.  **UI 集成**:
    *   在 `CurveTool` 的UI面板上增加对应的滑块或输入框，允许用户实时调整边缘噪声的强度和尺度。

**预期效果**: 绘制出的道路或河流边缘将呈现自然的、非均匀的形态，与周围地貌的过渡不再是一条生硬的渐变线。

### 任务 2: 实现“二次效应”绘制系统

**目标**: 让曲线能够对其周围环境产生可信的物理影响，例如河流使土地湿润，道路压实地面，增强绘制物与环境的交互感和逻辑自洽性。

**具体实施项**:

1.  **创建全局效应图层 (`EffectMap`)**:
    *   在 `TilemapLayerRenderer` 中管理一个或多个全局的 `RenderTexture`，作为“效应图层”。
    *   该图层的不同通道可用于存储不同的效应值（例如：R通道 = 湿润度, G通道 = 压实度）。

2.  **扩展 `DrawCurveStrokeGPU` 功能**:
    *   修改该方法，使其在绘制主材质权重的同时，也能根据设置向 `EffectMap` 中写入相应的效应值。
    *   效应值的写入范围通常会比主材质的范围略大（例如，湿润效果会延伸到河流岸边之外）。

3.  **新增工具参数 (`CurveStrokeSettings`)**:
    *   `secondaryEffect` (enum): 枚举类型，包含 `None`, `Wetness`, `Compaction` 等选项。
    *   `secondaryEffectRadius` (float): 控制二次效应影响的范围。
    *   `secondaryEffectStrength` (float): 控制二次效应的强度。

4.  **修改最终渲染着色器**:
    *   修改 `TilemapChunk` 的渲染着色器，使其在最后阶段采样 `EffectMap`。
    *   根据采样到的效应值，动态调整基础材质的外观（例如，增加湿润度值会使地表颜色变深、反光增强）。

**预期效果**: 曲线绘制物将不再是孤立的，它会真实地“改变”其周围的环境，从而极大地提升了场景的融合度和真实感。

---

## 二、 未来扩展（非优先）(Future Extensions / Lower Priority)

### 任务 3: 实现基于高度图的“置换式”混合

**目标**: 引入物理高度概念，使材质混合更具层次感和立体感，例如石块路面能“嵌入”草地中。

**简述**: 此功能需要为地形材质提供配套的高度图，并重写 `Tilemap` 的最终混合着色器，使其在混合时比较高度值，让高处材质“覆盖”低处材质。

### 任务 4: 优化混合算法以保留细节

**目标**: 解决标准线性混合导致的边缘模糊和细节丢失问题。

**简述**: 此功能依赖于任务3的高度图系统。通过实现如“最大值混合”或“高度引导混合”等高级算法，可以在过渡区域更好地保留材质本身的纹理质感，使融合效果更清晰。
