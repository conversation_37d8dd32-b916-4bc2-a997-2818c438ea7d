# 地形绘制工具优化方案

- **版本**: 1.0
- **日期**: 2025-07-04
- **范围**: CurveTool（曲线工具）和 BrushTool（画笔工具）
- **目标**: 提升地形绘制工具的视觉质量、用户体验和技术架构

---

## 一、优化概述

### 1.1 核心问题识别

**当前问题**：
- **边缘过于规整**：两个工具都产生过于完美的几何边界，缺乏自然感
- **人工痕迹明显**：绘制结果显得"数字化"，缺乏有机的地形特征
- **功能重复**：两个工具有大量相似的GPU计算逻辑，但缺乏代码复用
- **预览系统不完善**：CurveTool缺乏实时预览，用户体验不佳

### 1.2 优化目标

**视觉质量提升**：
- 实现自然的边缘渗透效果
- 添加程序化噪声增强真实感
- 改进材质混合算法

**用户体验改善**：
- 完善预览系统
- 统一参数控制界面
- 增强错误处理和反馈

**技术架构优化**：
- 代码复用和模块化
- 性能监控和优化
- 内存管理改进

---

## 二、高优先级优化任务

### 任务 1: 实现边缘渗透与程序化噪声

**适用工具**: CurveTool + BrushTool

#### 技术实现

**1. 创建共享噪声函数库**
```hlsl
// Assets/MapEditor/Resources/Shaders/NoiseLibrary.hlsl
#ifndef NOISE_LIBRARY_INCLUDED
#define NOISE_LIBRARY_INCLUDED

// 2D Simplex噪声 - 高效的程序化噪声
float SimplexNoise2D(float2 pos, float scale)
{
    // 基于格点的噪声算法
    // 返回 (-1, 1) 范围的噪声值
}

// 分形噪声 - 多层次细节
float FractalNoise2D(float2 pos, float scale, int octaves)
{
    float result = 0.0;
    float amplitude = 1.0;
    float frequency = scale;
    
    for(int i = 0; i < octaves; i++)
    {
        result += SimplexNoise2D(pos * frequency, 1.0) * amplitude;
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    return result;
}

#endif
```

**2. 修改CurveStroke.compute着色器**
```hlsl
#include "NoiseLibrary.hlsl"

// 新增参数
float _EdgeNoiseStrength;  // 0-1，噪声强度
float _EdgeNoiseScale;     // 0.1-10，噪声频率
int _EdgeNoiseOctaves;     // 1-4，噪声层数

// 在DistanceToMask函数中应用噪声
float DistanceToMask(float distance, float strokeWidth, float edgeSoftness)
{
    if (distance <= 0.0) return 1.0;
    
    float softEdgeWidth = strokeWidth * edgeSoftness * 0.5;
    if (softEdgeWidth <= 0.0) return 0.0;
    
    // 应用边缘噪声
    if (_EdgeNoiseStrength > 0.0)
    {
        float2 worldPos = _ChunkOffset + pixelCenter / _PixelsPerUnit;
        float noise = FractalNoise2D(worldPos, _EdgeNoiseScale, _EdgeNoiseOctaves);
        
        // 只在边缘区域应用噪声
        float edgeInfluence = 1.0 - saturate(distance / softEdgeWidth);
        distance += noise * _EdgeNoiseStrength * strokeWidth * 0.1 * edgeInfluence;
    }
    
    float normalizedDistance = distance / softEdgeWidth;
    return 1.0 - smoothstep(0.0, 1.0, normalizedDistance);
}
```

**3. 修改SplatBrush.compute着色器**
```hlsl
#include "NoiseLibrary.hlsl"

// 新增参数（与CurveTool保持一致）
float _EdgeNoiseStrength;
float _EdgeNoiseScale;
int _EdgeNoiseOctaves;

[numthreads(8,8,1)]
void CSMain (uint3 id : SV_DispatchThreadID)
{
    // 现有代码...
    float mask = _MaskTex.SampleLevel(sampler_LinearClamp, uv, 0).r;
    
    // 应用边缘噪声
    if (_EdgeNoiseStrength > 0.0 && mask > 0.01 && mask < 0.99)
    {
        float2 worldPos = _ChunkOffset + (float2)dstInt / _PixelsPerUnit;
        float noise = FractalNoise2D(worldPos, _EdgeNoiseScale, _EdgeNoiseOctaves);
        
        // 边缘区域检测
        float edgeFactor = 1.0 - abs(mask - 0.5) * 2.0;
        edgeFactor = smoothstep(0.0, 1.0, edgeFactor);
        
        mask += noise * _EdgeNoiseStrength * edgeFactor;
        mask = saturate(mask);
    }
    
    // 继续现有逻辑...
}
```

#### 参数扩展

**CurveStrokeSettings扩展**
```csharp
[System.Serializable]
public struct CurveStrokeSettings
{
    // 现有参数...
    public float hardness;
    
    // 新增：边缘噪声参数
    public bool enableEdgeNoise;      // 噪声开关
    public float edgeNoiseStrength;   // 0-1，噪声强度
    public float edgeNoiseScale;      // 0.1-10，噪声频率
    public int edgeNoiseOctaves;      // 1-4，噪声层数
}
```

**BrushSettings扩展**
```csharp
public struct BrushSettings
{
    // 现有参数...
    public float bilateralFactor;
    
    // 新增：边缘噪声参数（与CurveTool保持一致）
    public bool enableEdgeNoise;
    public float edgeNoiseStrength;
    public float edgeNoiseScale;
    public int edgeNoiseOctaves;
}
```

#### 预期效果
- **CurveTool**: 道路和河流边缘呈现自然的侵蚀和生长痕迹
- **BrushTool**: 笔触边缘具有手绘质感，消除数字化痕迹
- **整体**: 地形绘制结果更加自然真实，融合度显著提升

### 任务 2: 完善预览系统

**主要针对**: CurveTool（BrushTool已有完善预览）

#### 实现方案

**1. 创建CurvePreviewRenderer**
```csharp
// Assets/MapEditor/Scripts/Tools/CurvePreviewRenderer.cs
public class CurvePreviewRenderer : MonoBehaviour, IRenderable
{
    private LineRenderer lineRenderer;
    private List<Vector3> previewPoints = new List<Vector3>();
    
    public void SetCurveSegments(IReadOnlyList<CurveSegment> segments)
    {
        previewPoints.Clear();
        foreach (var segment in segments)
        {
            previewPoints.Add(new Vector3(segment.position.x, segment.position.y, 0));
        }
        
        if (lineRenderer != null)
        {
            lineRenderer.positionCount = previewPoints.Count;
            lineRenderer.SetPositions(previewPoints.ToArray());
        }
    }
    
    public void SetStrokeWidth(float width)
    {
        if (lineRenderer != null)
        {
            lineRenderer.startWidth = width;
            lineRenderer.endWidth = width;
        }
    }
    
    public void SetMaterialPreview(int materialIndex)
    {
        // 根据材质索引设置预览颜色
        var groundTextures = GroundTextureProvider.GetAllTextures();
        if (materialIndex < groundTextures.Length)
        {
            // 使用材质的主色调作为预览颜色
            Color previewColor = ExtractDominantColor(groundTextures[materialIndex].diffuseTexture);
            previewColor.a = 0.7f; // 半透明
            
            if (lineRenderer != null)
            {
                lineRenderer.material.color = previewColor;
            }
        }
    }
}
```

**2. 集成到CurveTool**
```csharp
// 在CurveTool.cs中添加
private CurvePreviewRenderer previewRenderer;

public override void OnActivate()
{
    base.OnActivate();
    
    // 创建预览渲染器
    if (previewRenderer == null)
    {
        GameObject rendererObj = new("CurvePreviewRenderer");
        previewRenderer = rendererObj.AddComponent<CurvePreviewRenderer>();
        previewRenderer.Initialize();
    }
    
    previewRenderer.gameObject.SetActive(true);
    UpdatePreviewFromSettings();
}

private void UpdatePreviewFromSettings()
{
    previewRenderer?.SetStrokeWidth(strokeSettings.width);
    previewRenderer?.SetMaterialPreview(strokeSettings.materialIndex);
    previewRenderer?.SetCurveSegments(curveSegments);
}
```

### 任务 3: 性能优化与内存管理

#### 实现RenderTexture对象池

**1. 创建对象池管理器**
```csharp
// Assets/MapEditor/Scripts/Rendering/RenderTexturePool.cs
public class RenderTexturePool : MonoBehaviour
{
    private static RenderTexturePool instance;
    private Dictionary<string, Queue<RenderTexture>> pools = new();
    
    public static RenderTexture GetTemporary(int width, int height, RenderTextureFormat format)
    {
        string key = $"{width}x{height}_{format}";
        
        if (instance.pools.TryGetValue(key, out var pool) && pool.Count > 0)
        {
            return pool.Dequeue();
        }
        
        // 创建新的RT
        var rt = new RenderTexture(width, height, 0, format)
        {
            enableRandomWrite = true,
            filterMode = FilterMode.Bilinear
        };
        rt.Create();
        return rt;
    }
    
    public static void ReleaseTemporary(RenderTexture rt)
    {
        if (rt == null) return;
        
        string key = $"{rt.width}x{rt.height}_{rt.format}";
        
        if (!instance.pools.ContainsKey(key))
        {
            instance.pools[key] = new Queue<RenderTexture>();
        }
        
        // 清理RT内容
        RenderTexture.active = rt;
        GL.Clear(true, true, Color.clear);
        RenderTexture.active = null;
        
        instance.pools[key].Enqueue(rt);
    }
}
```

**2. 修改TilemapLayerRenderer使用对象池**
```csharp
// 替换所有 RenderTexture.GetTemporary 调用
// 从：
RenderTexture maskRT = RenderTexture.GetTemporary(maskPixelSize, maskPixelSize, 0, RenderTextureFormat.R8);

// 到：
RenderTexture maskRT = RenderTexturePool.GetTemporary(maskPixelSize, maskPixelSize, RenderTextureFormat.R8);

// 替换所有 RenderTexture.ReleaseTemporary 调用
// 从：
RenderTexture.ReleaseTemporary(maskRT);

// 到：
RenderTexturePool.ReleaseTemporary(maskRT);
```

#### 性能监控系统

**1. 创建性能监控器**
```csharp
// Assets/MapEditor/Scripts/Tools/PerformanceMonitor.cs
public class PerformanceMonitor
{
    private float totalDrawTime;
    private int drawCallCount;
    private int chunksAffectedCount;
    
    public void BeginDraw()
    {
        drawStartTime = Time.realtimeSinceStartup;
    }
    
    public void EndDraw(int chunksAffected)
    {
        float drawTime = Time.realtimeSinceStartup - drawStartTime;
        totalDrawTime += drawTime;
        drawCallCount++;
        chunksAffectedCount += chunksAffected;
    }
    
    public PerformanceStats GetStats()
    {
        return new PerformanceStats
        {
            averageDrawTime = drawCallCount > 0 ? totalDrawTime / drawCallCount : 0,
            chunksPerSecond = totalDrawTime > 0 ? chunksAffectedCount / totalDrawTime : 0,
            totalDrawCalls = drawCallCount
        };
    }
}
```

### 任务 3: 修复硬度参数的一致性问题

**适用工具**: CurveTool + BrushTool

#### 当前问题识别

**1. UI范围设置不一致**
```xml
<!-- BrushTool: 硬度范围 0.1-5 -->
<ui:Slider name="HardnessSlider" low-value="0.1" high-value="5" />

<!-- CurveTool: 硬度范围 0-1 -->
<ui:Slider name="HardnessSlider" low-value="0" high-value="1" />
```

**问题**: 两个工具的UI滑块范围完全不同，但代码中都被限制到0-1范围，导致用户困惑。

**2. 硬度映射方向相反**
```csharp
// BrushTool: UI 0-1 映射到 4.0-0.25 (反向)
float mappedHardness = Mathf.Lerp(4.0f, 0.25f, settings.hardness);

// CurveTool: UI 0-1 映射到 0.25-4.0 (正向)
float mappedHardness = Mathf.Lerp(0.25f, 4.0f, settings.hardness);
```

**问题**: 相同的UI值在两个工具中产生相反的效果，用户体验极其混乱。

**3. 硬度效果在中间值区域才明显**
```hlsl
float effectiveMask = pow(saturate(mask), _Hardness);
// 当mask接近0或1时，pow效果不明显
// 只有在0.3-0.7区域硬度变化才显著
```

**问题**: 用户调节硬度时感受不到明显变化，特别是在边缘区域。

**4. 缺少实时预览反馈**

当前预览系统不显示硬度效果，用户只能在实际绘制后才能看到差异。

#### 解决方案

**1. 统一硬度映射系统**
```csharp
// Assets/MapEditor/Scripts/Tools/HardnessMapping.cs
public static class HardnessMapping
{
    // 统一的UI范围：0-1
    public const float UI_MIN = 0f;
    public const float UI_MAX = 1f;

    // 统一的着色器范围：0.1-5.0
    public const float SHADER_MIN = 0.1f;
    public const float SHADER_MAX = 5.0f;

    public static float UIToShader(float uiValue)
    {
        // 使用指数映射增强低值区域的敏感度
        float normalized = Mathf.Clamp01(uiValue);
        float exponential = Mathf.Pow(normalized, 0.5f);
        return Mathf.Lerp(SHADER_MIN, SHADER_MAX, exponential);
    }

    public static string GetHardnessDescription(float uiValue)
    {
        if (uiValue < 0.3f) return "柔和";
        if (uiValue < 0.7f) return "中等";
        return "锐利";
    }
}
```

**2. 改进硬度算法**
```hlsl
// 在NoiseLibrary.hlsl中添加改进的硬度函数
float ApplyEnhancedHardness(float mask, float hardness)
{
    // 分段处理，增强边缘区域效果
    if (mask < 0.1 || mask > 0.9)
    {
        // 在边缘区域应用更强的硬度效果
        return pow(saturate(mask), hardness * 2.0);
    }
    else
    {
        // 在中间区域应用标准硬度
        float powered = pow(saturate(mask), hardness);

        // 应用S曲线增强对比度
        float contrast = 1.0 + (hardness - 1.0) * 0.5;
        return saturate((powered - 0.5) * contrast + 0.5);
    }
}
```

**3. 统一UI控件配置**
```xml
<!-- 两个工具都使用相同的配置 -->
<ui:Slider name="HardnessSlider" low-value="0" high-value="1" value="0.5" />
<ui:Label name="HardnessDescription" text="中等" />
```

**4. 添加硬度预览**
```csharp
// 在预览渲染器中添加硬度效果显示
public void SetHardnessPreview(float hardness)
{
    // 更新预览材质的硬度参数
    if (previewMaterial != null)
    {
        float mappedHardness = HardnessMapping.UIToShader(hardness);
        previewMaterial.SetFloat("_PreviewHardness", mappedHardness);
    }

    // 更新UI描述文本
    hardnessDescriptionLabel.text = HardnessMapping.GetHardnessDescription(hardness);
}
```

#### 实施计划
1. **Week 1**: 创建统一的HardnessMapping类
2. **Week 2**: 修改两个工具的UI配置和映射逻辑
3. **Week 3**: 改进着色器中的硬度算法
4. **Week 4**: 添加预览功能和用户反馈

#### 预期效果
- 两个工具的硬度行为完全一致
- 硬度变化在整个范围内都明显可感知
- 用户可以通过预览立即看到硬度效果
- 提供描述性文本帮助用户理解当前设置

---

## 四、中优先级优化任务

### 任务 4: 硬度与噪声的协同优化

**目标**: 确保硬度参数在添加边缘噪声后仍然有效且有意义

#### 协同工作机制

**1. 智能硬度调节**
```csharp
// 根据噪声强度自动调节硬度效果
public struct SmartHardnessSettings
{
    public float baseHardness;           // 用户设置的基础硬度
    public bool adaptToNoiseStrength;    // 是否根据噪声强度自动调节
    public float noiseCompensationFactor; // 噪声补偿系数

    public float GetEffectiveHardness(float noiseStrength)
    {
        if (!adaptToNoiseStrength) return baseHardness;

        // 噪声强度高时，适当降低硬度避免过度锐化
        float compensation = Mathf.Lerp(1.0f, 0.7f, noiseStrength * noiseCompensationFactor);
        return baseHardness * compensation;
    }
}
```

**2. 上下文感知的硬度算法**
```hlsl
// 在着色器中实现上下文感知的硬度处理
float GetContextAwareHardness(float baseHardness, float noiseInfluence, float edgeSoftness)
{
    // 在噪声影响强的区域降低硬度，避免过度锐化
    float noiseCompensation = 1.0 - noiseInfluence * 0.3;

    // 在柔和边缘区域适当提高硬度，保持清晰度
    float softnessCompensation = 1.0 + edgeSoftness * 0.2;

    // 确保结果在合理范围内
    return clamp(baseHardness * noiseCompensation * softnessCompensation, 0.1, 5.0);
}

// 在CurveStroke.compute和SplatBrush.compute中应用
float contextHardness = GetContextAwareHardness(_Hardness, noiseInfluence, _EdgeSoftness);
float effectiveMask = pow(saturate(mask), contextHardness);
```

**3. 材质感知的硬度预设**
```csharp
// 为不同材质类型提供推荐的硬度设置
public enum MaterialHardnessProfile
{
    Soft,    // 泥土、沙子 - 硬度 0.2-0.4
    Medium,  // 草地、碎石 - 硬度 0.4-0.7
    Hard,    // 石板、金属 - 硬度 0.7-1.0
    Custom   // 用户自定义
}

[CreateAssetMenu(fileName = "MaterialProfile", menuName = "MapEditor/Material Profile")]
public class MaterialProfileSO : ScriptableObject
{
    [Header("硬度设置")]
    public MaterialHardnessProfile hardnessProfile = MaterialHardnessProfile.Medium;
    public float recommendedHardness = 0.5f;

    [Header("噪声设置")]
    public float recommendedNoiseStrength = 0.3f;
    public float recommendedNoiseScale = 2.0f;

    [Header("协同参数")]
    public bool enableSmartHardness = true;
    public float noiseHardnessBalance = 0.5f; // 噪声与硬度的平衡点
}
```

#### UI改进方案

**1. 硬度效果实时预览**
```csharp
// 在UI面板中添加硬度效果预览
public class HardnessPreviewGenerator
{
    public static Texture2D GenerateHardnessPreview(float hardness, float noiseStrength, int width = 128)
    {
        var texture = new Texture2D(width, 32, TextureFormat.RGBA32, false);

        for (int x = 0; x < width; x++)
        {
            float mask = (float)x / width; // 0到1的渐变

            // 应用噪声影响
            if (noiseStrength > 0)
            {
                float noise = Mathf.PerlinNoise(x * 0.1f, 0) * 2f - 1f;
                mask += noise * noiseStrength * 0.1f;
                mask = Mathf.Clamp01(mask);
            }

            // 应用硬度
            float hardnessEffect = Mathf.Pow(mask, HardnessMapping.UIToShader(hardness));

            for (int y = 0; y < 32; y++)
            {
                texture.SetPixel(x, y, new Color(hardnessEffect, hardnessEffect, hardnessEffect, 1f));
            }
        }

        texture.Apply();
        return texture;
    }
}
```

**2. 参数联动提示**
```csharp
// 在UI中显示参数之间的关系提示
public class ParameterRelationshipHints
{
    public static string GetHardnessNoiseHint(float hardness, float noiseStrength)
    {
        if (hardness > 0.8f && noiseStrength > 0.6f)
            return "⚠️ 高硬度+强噪声可能产生过度锐化效果";

        if (hardness < 0.3f && noiseStrength < 0.2f)
            return "💡 建议提高硬度或噪声强度以增强效果";

        if (hardness > 0.6f && noiseStrength > 0.4f)
            return "✅ 硬度与噪声配置良好";

        return "";
    }
}
```

### 任务 5: 创建统一的地形绘制基类

**目标**: 减少代码重复，提升维护性

```csharp
// Assets/MapEditor/Scripts/Tools/TerrainPaintingToolBase.cs
public abstract class TerrainPaintingToolBase : MapToolBase
{
    protected PerformanceMonitor performanceMonitor;
    protected MapService mapService;
    
    // 通用的噪声参数
    protected struct NoiseSettings
    {
        public bool enabled;
        public float strength;
        public float scale;
        public int octaves;
    }
    
    protected NoiseSettings noiseSettings;
    
    // 抽象方法，由子类实现
    protected abstract void DrawGPU(Vector2 position);
    protected abstract void UpdatePreview();
    
    // 通用的错误处理
    protected bool ValidateDrawingContext()
    {
        var renderer = mapService.GetActiveLayerRenderer() as TilemapLayerRenderer;
        if (renderer == null)
        {
            PublishToolStatusChanged("错误：无法获取有效的地形渲染器");
            return false;
        }
        return true;
    }
    
    // 通用的噪声参数设置
    public void SetNoiseSettings(NoiseSettings settings)
    {
        noiseSettings = settings;
        UpdatePreview();
    }
}
```

### 任务 6: 改进错误处理和用户反馈

**1. 统一错误处理机制**
```csharp
public enum ToolErrorType
{
    NoActiveLayer,
    InvalidRenderer,
    InsufficientMemory,
    ShaderCompilationFailed,
    InvalidParameters
}

public class ToolErrorHandler
{
    public static void HandleError(ToolErrorType errorType, string details = "")
    {
        string message = GetErrorMessage(errorType, details);
        
        // 记录日志
        Debug.LogError($"[TerrainTool] {message}");
        
        // 显示用户友好的提示
        ShowUserNotification(message);
        
        // 发送错误事件
        MapEditorCore.Instance.EventSystem?.Publish(new ToolErrorEvent
        {
            ErrorType = errorType,
            Message = message,
            Timestamp = System.DateTime.Now
        });
    }
}
```

---

## 四、实施计划

### 阶段 1: 核心功能实现（3-4周）
1. **Week 1**: 实现共享噪声函数库和CurveTool噪声功能
2. **Week 2**: 移植噪声功能到BrushTool，创建预览系统
3. **Week 3**: 修复硬度参数一致性问题
4. **Week 4**: 实现硬度与噪声的协同优化

### 阶段 2: 架构优化（2周）
1. **Week 5**: 创建统一基类，重构现有代码
2. **Week 6**: 性能优化和对象池实现

### 阶段 3: 完善和测试（1-2周）
1. **Week 7**: 改进错误处理，完善监控系统
2. **Week 8**: 全面测试，性能调优，文档更新

---

## 五、预期收益

### 视觉质量提升
- **自然度提升 80%**: 边缘噪声消除人工痕迹
- **真实感增强**: 程序化细节模拟自然地形特征
- **艺术表现力**: 为用户提供更多创作可能性

### 用户体验改善
- **预览系统**: 实时反馈，所见即所得
- **错误处理**: 清晰的问题提示和解决建议
- **性能稳定**: 内存优化减少卡顿现象

### 技术架构优化
- **代码复用率提升 60%**: 统一基类和共享组件
- **维护成本降低**: 模块化设计便于后续扩展
- **性能监控**: 数据驱动的优化决策

---

## 六、风险评估与缓解

### 技术风险
- **性能影响**: 噪声计算增加GPU负载 → 提供质量预设选项
- **兼容性问题**: 着色器修改可能影响现有功能 → 渐进式部署
- **内存使用**: 对象池可能占用更多内存 → 智能清理机制
- **硬度参数混乱**: 修改硬度系统可能影响现有用户工作流程 → 提供迁移工具和向后兼容选项

### 实施风险
- **开发时间**: 功能复杂度可能超出预期 → 分阶段实施
- **测试覆盖**: 新功能需要充分测试 → 自动化测试流程
- **用户接受度**: 界面变化可能影响用户习惯 → 保持向后兼容
- **参数调优复杂度**: 硬度与噪声的协同调节可能增加学习成本 → 提供智能预设和引导教程

---

## 七、成功指标

### 定量指标
- GPU绘制性能提升 15%
- 内存使用优化 20%
- 代码重复率降低 60%
- 用户报告的绘制问题减少 80%
- 硬度参数用户困惑报告减少 90%
- 参数调节的有效感知度提升 70%

### 定性指标
- 用户满意度调查评分提升
- 社区反馈中关于"自然度"的正面评价增加
- 开发团队维护效率提升
- 硬度与噪声协同使用的用户接受度
- 新用户学习曲线的平缓程度

---

## 八、技术细节补充

### 8.1 噪声函数优化

**GPU友好的Simplex噪声实现**：
```hlsl
// 优化的2D Simplex噪声，减少分支和纹理采样
float SimplexNoise2D(float2 pos, float scale)
{
    pos *= scale;

    // 简化的格点计算
    float2 i = floor(pos + (pos.x + pos.y) * 0.366025403);
    float2 x0 = pos - i + (i.x + i.y) * 0.211324865;

    // 确定三角形
    float2 i1 = (x0.x > x0.y) ? float2(1.0, 0.0) : float2(0.0, 1.0);
    float2 x1 = x0 - i1 + 0.211324865;
    float2 x2 = x0 - 1.0 + 2.0 * 0.211324865;

    // 计算梯度贡献
    float3 p = frac(float3(i, i.x + i.y) * 0.024390243);
    p += dot(p, p.yzx + 19.19);
    float3 g = frac((p.xxy + p.yzz) * p.zyx) * 2.0 - 1.0;

    float3 m = max(0.5 - float3(dot(x0, x0), dot(x1, x1), dot(x2, x2)), 0.0);
    m = m * m * m * m;

    return 130.0 * dot(m, float3(dot(g.xy, x0), dot(g.zw, x1), dot(g.xy, x2)));
}
```

### 8.2 UI集成示例

**CurveToolPanel噪声控件**：
```csharp
// 在CurveToolPanel.cs中添加噪声控制
private void CreateNoiseControls(VisualElement parent)
{
    var noiseSection = new Foldout { text = "边缘噪声", value = false };

    var enableToggle = new Toggle("启用噪声");
    var strengthSlider = new Slider("强度", 0f, 1f) { value = 0.2f };
    var scaleSlider = new Slider("频率", 0.1f, 10f) { value = 2.0f };
    var octavesSlider = new SliderInt("层数", 1, 4) { value = 2 };

    // 事件绑定
    enableToggle.RegisterValueChangedCallback(evt =>
        curveTool?.SetNoiseEnabled(evt.newValue));
    strengthSlider.RegisterValueChangedCallback(evt =>
        curveTool?.SetNoiseStrength(evt.newValue));

    noiseSection.Add(enableToggle);
    noiseSection.Add(strengthSlider);
    noiseSection.Add(scaleSlider);
    noiseSection.Add(octavesSlider);

    parent.Add(noiseSection);
}
```

### 8.3 性能基准测试

**测试场景配置**：
- 地图大小：2048x2048像素
- Chunk大小：256x256像素
- 测试设备：GTX 1060 6GB / RTX 3070

**预期性能指标**：
```
绘制操作          | 当前耗时 | 优化后耗时 | 提升幅度
----------------|---------|-----------|----------
单次画笔绘制      | 2.1ms   | 1.8ms     | 14%
曲线绘制(10段)    | 8.5ms   | 7.2ms     | 15%
模糊处理(半径2)   | 3.2ms   | 2.8ms     | 12%
内存分配         | 45MB    | 36MB      | 20%
```

---

## 九、扩展功能规划

### 9.1 高级噪声类型

**未来可添加的噪声类型**：
1. **Worley噪声**: 适合模拟细胞状结构
2. **Ridged噪声**: 适合山脊和峡谷效果
3. **Turbulence**: 适合云朵和流体效果
4. **Domain Warping**: 高级变形效果

### 9.2 材质感知的边缘处理

**智能边缘算法**：
```csharp
// 根据材质类型调整噪声参数
public struct MaterialAwareNoiseSettings
{
    public TerrainMaterialType materialType;
    public float baseNoiseStrength;
    public float erosionFactor;      // 侵蚀系数
    public float vegetationGrowth;   // 植被生长系数
}

// 示例：不同材质的噪声配置
var materialConfigs = new Dictionary<TerrainMaterialType, MaterialAwareNoiseSettings>
{
    [TerrainMaterialType.Rock] = new() {
        baseNoiseStrength = 0.8f,
        erosionFactor = 0.3f
    },
    [TerrainMaterialType.Grass] = new() {
        baseNoiseStrength = 0.4f,
        vegetationGrowth = 0.7f
    },
    [TerrainMaterialType.Sand] = new() {
        baseNoiseStrength = 0.6f,
        erosionFactor = 0.8f
    }
};
```

### 9.3 预设系统

**噪声预设管理**：
```csharp
[CreateAssetMenu(fileName = "NoisePreset", menuName = "MapEditor/Noise Preset")]
public class NoisePresetSO : ScriptableObject
{
    public string presetName;
    public string description;
    public NoiseSettings settings;
    public Texture2D previewTexture;

    [Header("适用场景")]
    public TerrainType[] suitableTerrains;
    public string[] tags;
}
```

---

## 十、维护和支持

### 10.1 调试工具

**噪声可视化器**：
```csharp
// 开发时用于调试噪声效果的工具
public class NoiseVisualizer : EditorWindow
{
    private NoiseSettings settings;
    private Texture2D previewTexture;

    void OnGUI()
    {
        // 实时预览噪声效果
        settings.strength = EditorGUILayout.Slider("强度", settings.strength, 0f, 1f);
        settings.scale = EditorGUILayout.Slider("频率", settings.scale, 0.1f, 10f);

        if (GUILayout.Button("生成预览"))
        {
            GenerateNoisePreview();
        }

        if (previewTexture != null)
        {
            GUILayout.Label(previewTexture);
        }
    }
}
```

### 10.2 性能分析器

**GPU性能监控**：
```csharp
public class GPUProfiler
{
    private CommandBuffer commandBuffer;
    private Dictionary<string, float> timings = new();

    public void BeginSample(string sampleName)
    {
        commandBuffer.BeginSample(sampleName);
    }

    public void EndSample(string sampleName)
    {
        commandBuffer.EndSample(sampleName);
    }

    public void GenerateReport()
    {
        // 生成性能报告
        var report = new StringBuilder();
        report.AppendLine("=== GPU性能报告 ===");

        foreach (var timing in timings.OrderByDescending(t => t.Value))
        {
            report.AppendLine($"{timing.Key}: {timing.Value:F2}ms");
        }

        Debug.Log(report.ToString());
    }
}
```

---

## 十一、总结

本优化方案通过以下核心改进，将显著提升地形绘制工具的质量：

**技术创新**：
- 程序化噪声系统消除人工痕迹
- 统一的GPU计算架构提升性能
- 智能内存管理减少资源浪费

**用户体验**：
- 完善的预览系统提供即时反馈
- 直观的参数控制界面
- 可靠的错误处理机制

**架构优化**：
- 模块化设计便于维护和扩展
- 代码复用减少重复开发
- 性能监控支持数据驱动优化

通过分阶段实施，该方案将在保持系统稳定性的同时，为用户提供更加自然、高效的地形绘制体验。

---

*本文档版本：1.0 | 最后更新：2025-07-04 | 下次审查：2025-08-04*
